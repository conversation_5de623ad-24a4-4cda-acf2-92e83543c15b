<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('currency')->after('amount');
            $table->string('attachment_path')->nullable()->after('description');
            $table->renameColumn('date', 'executed_at');
            $table->dateTime('executed_at')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn(['currency', 'attachment_path']);
            $table->renameColumn('executed_at', 'date');
            $table->date('date')->change();
        });
    }
}; 